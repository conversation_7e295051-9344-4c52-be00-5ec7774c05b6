package com.example.demo.service;

import com.example.demo.model.HealthCheck;
import com.example.demo.dao.HealthCheckDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 体检指标服务层
 * 提供体检数据的业务逻辑处理
 */
@Service
public class HealthCheckService {

    private static final Logger logger = LoggerFactory.getLogger(HealthCheckService.class);
    private final HealthCheckDAO healthCheckDAO;

    @Autowired
    public HealthCheckService(HealthCheckDAO healthCheckDAO) {
        this.healthCheckDAO = healthCheckDAO;
    }

    /**
     * 根据姓名获取体检指标
     * @param personName 人员姓名
     * @return 体检数据，如果未找到返回null
     */
    public HealthCheck getHealthCheckByPersonName(String personName) {
        if (personName == null || personName.trim().isEmpty()) {
            logger.warn("查询体检数据时姓名为空");
            return null;
        }

        logger.debug("查询体检数据：姓名={}", personName);
        HealthCheck result = healthCheckDAO.findByPersonName(personName.trim());

        if (result == null) {
            logger.info("未找到姓名为 {} 的体检数据", personName);
        } else {
            logger.debug("找到体检数据：{}", result.getHealthSummary());
        }

        return result;
    }

    /**
     * 保存体检指标
     * @param healthCheck 体检数据
     * @return 是否保存成功
     */
    public boolean saveHealthCheck(HealthCheck healthCheck) {
        if (healthCheck == null) {
            logger.warn("尝试保存空的体检数据");
            return false;
        }

        if (healthCheck.getPersonName() == null || healthCheck.getPersonName().trim().isEmpty()) {
            logger.warn("尝试保存体检数据但姓名为空");
            return false;
        }

        logger.info("保存体检数据：{}", healthCheck.getPersonName());
        boolean success = healthCheckDAO.save(healthCheck) > 0;

        if (success) {
            logger.info("体检数据保存成功：{}", healthCheck.getPersonName());
        } else {
            logger.error("体检数据保存失败：{}", healthCheck.getPersonName());
        }

        return success;
    }

    /**
     * 检查数据库连接状态
     * @return 数据库是否可用
     */
    public boolean isDatabaseAvailable() {
        try {
            // 尝试查询一个已知存在的记录来测试数据库连接
            HealthCheck testResult = healthCheckDAO.findByPersonName("张三");
            return true;
        } catch (Exception e) {
            logger.error("数据库连接检查失败", e);
            return false;
        }
    }
}