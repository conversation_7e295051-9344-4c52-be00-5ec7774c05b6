spring.application.name=health-check-mcp
server.port=8081
spring.datasource.url=********************************************
spring.datasource.username=mcpuser
spring.datasource.password=mcppass
spring.datasource.driver-class-name=org.postgresql.Driver

spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql

# JSON ??
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.serialization.fail-on-empty-beans=false

# HTTP ??
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

logging.level.root=INFO
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG
