package com.example.demo.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class JsonRpcResponse {
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    private Object result;
    private JsonRpcError error;
    private Object id;

    public JsonRpcResponse(Object id) {
        this.id = id;
    }

    // Getters and setters
    public String getJsonrpc() { return jsonrpc; }
    public Object getResult() { return result; }
    public void setResult(Object result) { this.result = result; }
    
    public JsonRpcError getError() { return error; }
    public void setError(JsonRpcError error) { this.error = error; }
    
    public Object getId() { return id; }
}