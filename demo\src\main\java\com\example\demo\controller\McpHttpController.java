package com.example.demo.controller;

import com.example.demo.mcp.model.*;
import com.example.demo.service.HealthCheckService;
import com.example.demo.model.HealthCheck;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
@RestController
@RequestMapping("/mcp")
@CrossOrigin(origins = "*")
public class McpHttpController {

    private static final Logger logger = LoggerFactory.getLogger(McpHttpController.class);
    private final HealthCheckService healthCheckService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private boolean initialized = false;

    public McpHttpController(HealthCheckService healthCheckService) {
        this.healthCheckService = healthCheckService;
    }

    /**
     * SSE连接端点 - 支持Dify平台的Server-Sent Events连接
     */
    @GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter handleSse() {
        logger.info("收到 SSE 连接请求");
        SseEmitter emitter = new SseEmitter(0L);

        try {
            Map<String, Object> serverInfo = new HashMap<>();
            serverInfo.put("name", "health-check-mcp");
            serverInfo.put("version", "1.0.0");
            serverInfo.put("protocolVersion", "2024-11-05");

            emitter.send(SseEmitter.event()
                    .name("server_info")
                    .data(serverInfo));
        } catch (IOException e) {
            logger.error("SSE 发送失败", e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * JSON-RPC 2.0 协议处理端点
     */
    @PostMapping(value = "/rpc", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<JsonRpcResponse> handleJsonRpc(@RequestBody JsonRpcRequest request) {
        logger.info("收到 JSON-RPC 请求: method={}, id={}, params={}",
            request.getMethod(), request.getId(), request.getParams());

        JsonRpcResponse response = new JsonRpcResponse(request.getId());

        try {
            switch (request.getMethod()) {
                case "initialize":
                    response.setResult(handleInitialize(request.getParams()));
                    initialized = true;
                    break;

                case "tools/list":
                    response.setResult(handleToolsList());
                    break;

                case "tools/call":
                    response.setResult(handleToolsCall(request.getParams()));
                    break;

                default:
                    logger.warn("未知的方法: {}", request.getMethod());
                    response.setError(new JsonRpcError(-32601, "Method not found: " + request.getMethod()));
            }

            logger.info("JSON-RPC 请求处理成功: method={}, id={}", request.getMethod(), request.getId());

        } catch (IllegalArgumentException e) {
            logger.warn("参数错误: {}", e.getMessage());
            response.setError(new JsonRpcError(-32602, "Invalid params: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("处理 JSON-RPC 请求时发生错误: method={}, params={}",
                request.getMethod(), request.getParams(), e);
            response.setError(new JsonRpcError(-32603, "Internal error: " + e.getMessage()));
        }

        // 添加CORS头以确保Dify可以访问
        return ResponseEntity.ok()
                .header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
                .header("Access-Control-Allow-Headers", "Content-Type")
                .body(response);
    }

    /**
     * 处理CORS预检请求
     */
    @RequestMapping(value = "/rpc", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        return ResponseEntity.ok()
                .header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
                .header("Access-Control-Allow-Headers", "Content-Type")
                .header("Access-Control-Max-Age", "3600")
                .build();
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "ok");
        healthInfo.put("service", "health-check-mcp");
        healthInfo.put("timestamp", System.currentTimeMillis());
        healthInfo.put("version", "1.0.0");

        // 检查数据库连接状态
        try {
            HealthCheck testQuery = healthCheckService.getHealthCheckByPersonName("张三");
            healthInfo.put("database_status", testQuery != null ? "connected" : "no_data");
        } catch (Exception e) {
            healthInfo.put("database_status", "error: " + e.getMessage());
        }

        return ResponseEntity.ok(healthInfo);
    }

    /**
     * 处理初始化请求
     */
    private Map<String, Object> handleInitialize(Map<String, Object> params) {
        logger.info("处理初始化请求: {}", params);

        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2024-11-05");
        result.put("capabilities", Map.of("tools", Map.of()));
        result.put("serverInfo", Map.of(
            "name", "health-check-mcp",
            "version", "1.0.0"
        ));

        return result;
    }

    /**
     * 处理工具列表请求
     */
    private Map<String, Object> handleToolsList() {
        logger.info("处理工具列表请求");

        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "get_health_check");
        tool.put("description", "根据姓名查询用户的体检指标数据");

        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        schema.put("properties", Map.of(
            "person_name", Map.of(
                "type", "string",
                "description", "要查询的人员姓名"
            )
        ));
        schema.put("required", List.of("person_name"));

        tool.put("inputSchema", schema);

        return Map.of("tools", List.of(tool));
    }

    /**
     * 处理工具调用请求
     */
    private Map<String, Object> handleToolsCall(Map<String, Object> params) {
        logger.info("处理工具调用请求: {}", params);

        String toolName = (String) params.get("name");
        if (!"get_health_check".equals(toolName)) {
            throw new IllegalArgumentException("Unknown tool: " + toolName);
        }

        // 处理arguments参数，可能是Map或JSON字符串
        Map<String, Object> arguments = parseArguments(params.get("arguments"));

        String personName = (String) arguments.get("person_name");
        if (personName == null || personName.trim().isEmpty()) {
            throw new IllegalArgumentException("person_name is required");
        }

        HealthCheck healthCheck = healthCheckService.getHealthCheckByPersonName(personName.trim());

        if (healthCheck == null) {
            return Map.of(
                "content", List.of(Map.of(
                    "type", "text",
                    "text", "未找到姓名为 \"" + personName + "\" 的体检数据"
                ))
            );
        }

        // 格式化体检数据
        String resultText = formatHealthCheckResult(healthCheck);

        return Map.of(
            "content", List.of(Map.of(
                "type", "text",
                "text", resultText
            ))
        );
    }

    /**
     * 格式化体检结果为可读文本
     */
    private String formatHealthCheckResult(HealthCheck healthCheck) {
        StringBuilder sb = new StringBuilder();
        sb.append("体检指标查询结果：\n\n");
        sb.append("姓名：").append(healthCheck.getPersonName()).append("\n");
        sb.append("性别：").append(healthCheck.getGender()).append("\n");
        sb.append("年龄：").append(healthCheck.getAge()).append("岁\n");
        sb.append("检查日期：").append(healthCheck.getCheckDate()).append("\n\n");

        sb.append("血常规指标：\n");
        sb.append("• 白细胞计数：").append(healthCheck.getWbcCount()).append(" × 10⁹/L\n");
        sb.append("• 中性粒细胞百分比：").append(healthCheck.getNeutrophilPct()).append("%\n");
        sb.append("• 淋巴细胞百分比：").append(healthCheck.getLymphocytePct()).append("%\n");
        sb.append("• 单核细胞百分比：").append(healthCheck.getMonocytePct()).append("%\n");
        sb.append("• 中性粒细胞计数：").append(healthCheck.getNeutrophilCount()).append(" × 10⁹/L\n");

        return sb.toString();
    }

    /**
     * 解析arguments参数，支持Map和JSON字符串两种格式
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseArguments(Object argumentsObj) {
        if (argumentsObj == null) {
            return new HashMap<>();
        }

        // 如果已经是Map，直接返回
        if (argumentsObj instanceof Map) {
            return (Map<String, Object>) argumentsObj;
        }

        // 如果是字符串，尝试解析为JSON
        if (argumentsObj instanceof String) {
            String argumentsStr = (String) argumentsObj;
            if (argumentsStr.trim().isEmpty()) {
                return new HashMap<>();
            }

            try {
                return objectMapper.readValue(argumentsStr, Map.class);
            } catch (Exception e) {
                logger.error("解析arguments JSON字符串失败: {}", argumentsStr, e);
                throw new IllegalArgumentException("Invalid arguments format: " + e.getMessage());
            }
        }

        logger.error("不支持的arguments类型: {}", argumentsObj.getClass().getName());
        throw new IllegalArgumentException("Unsupported arguments type: " + argumentsObj.getClass().getName());
    }
}
