package com.example.demo.model;

import java.time.LocalDate;

/**
 * 体检指标实体类
 */
public class HealthCheck {
    private Long id;
    private String personName;
    private String gender;
    private Integer age;
    private LocalDate checkDate;
    private Double wbcCount;
    private Double neutrophilPct;
    private Double lymphocytePct;
    private Double monocytePct;
    private Double neutrophilCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public LocalDate getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(LocalDate checkDate) {
        this.checkDate = checkDate;
    }

    public Double getWbcCount() {
        return wbcCount;
    }

    public void setWbcCount(Double wbcCount) {
        this.wbcCount = wbcCount;
    }

    public Double getNeutrophilPct() {
        return neutrophilPct;
    }

    public void setNeutrophilPct(Double neutrophilPct) {
        this.neutrophilPct = neutrophilPct;
    }

    public Double getLymphocytePct() {
        return lymphocytePct;
    }

    public void setLymphocytePct(Double lymphocytePct) {
        this.lymphocytePct = lymphocytePct;
    }

    public Double getMonocytePct() {
        return monocytePct;
    }

    public void setMonocytePct(Double monocytePct) {
        this.monocytePct = monocytePct;
    }

    public Double getNeutrophilCount() {
        return neutrophilCount;
    }

    public void setNeutrophilCount(Double neutrophilCount) {
        this.neutrophilCount = neutrophilCount;
    }

    /**
     * 检查白细胞计数是否正常
     * 正常范围：4.0-10.0 × 10⁹/L
     */
    public boolean isWbcCountNormal() {
        return wbcCount != null && wbcCount >= 4.0 && wbcCount <= 10.0;
    }

    /**
     * 检查中性粒细胞百分比是否正常
     * 正常范围：50%-70%
     */
    public boolean isNeutrophilPctNormal() {
        return neutrophilPct != null && neutrophilPct >= 50.0 && neutrophilPct <= 70.0;
    }

    /**
     * 检查淋巴细胞百分比是否正常
     * 正常范围：20%-40%
     */
    public boolean isLymphocytePctNormal() {
        return lymphocytePct != null && lymphocytePct >= 20.0 && lymphocytePct <= 40.0;
    }

    /**
     * 获取体检结果摘要
     */
    public String getHealthSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(personName).append("的体检结果：");

        if (!isWbcCountNormal()) {
            summary.append(" 白细胞计数异常");
        }
        if (!isNeutrophilPctNormal()) {
            summary.append(" 中性粒细胞比例异常");
        }
        if (!isLymphocytePctNormal()) {
            summary.append(" 淋巴细胞比例异常");
        }

        if (isWbcCountNormal() && isNeutrophilPctNormal() && isLymphocytePctNormal()) {
            summary.append(" 主要指标正常");
        }

        return summary.toString();
    }

    @Override
    public String toString() {
        return "HealthCheck{" +
                "id=" + id +
                ", personName='" + personName + '\'' +
                ", gender='" + gender + '\'' +
                ", age=" + age +
                ", checkDate=" + checkDate +
                ", wbcCount=" + wbcCount +
                ", neutrophilPct=" + neutrophilPct +
                ", lymphocytePct=" + lymphocytePct +
                ", monocytePct=" + monocytePct +
                ", neutrophilCount=" + neutrophilCount +
                '}';
    }
}