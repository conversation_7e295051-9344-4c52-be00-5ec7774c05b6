# 体检指标 MCP 服务

这是一个基于 Spring Boot 的体检指标 MCP (Model Context Protocol) 服务，使用 PostgreSQL 数据库存储体检数据，支持与 Dify 平台集成。

## 项目介绍

该项目提供了符合 MCP 协议的接口，用于查询用户的体检指标数据。支持 JSON-RPC 2.0 协议和 Server-Sent Events (SSE) 连接方式。

## 功能特点

- 基于 Spring Boot 2.7.14 和 PostgreSQL 数据库
- 支持 MCP 协议 (2024-11-05 版本)
- 提供 JSON-RPC 2.0 和 SSE 接口
- 支持通过用户姓名查询体检指标
- 完整的 CORS 支持，便于跨域访问
- 支持 Dify 平台集成

## 项目结构

```
health-check-mcp/
├── src/
│   ├── main/
│   │   ├── java/com/example/demo/
│   │   │   ├── DemoApplication.java              # 主启动类
│   │   │   ├── config/
│   │   │   │   ├── CorsConfig.java               # CORS 全局配置
│   │   │   │   ├── DatabaseConfig.java          # 数据库配置
│   │   │   │   ├── DifyMcpConfig.java           # Dify MCP 专用配置
│   │   │   │   └── WebConfig.java               # Web MVC 配置
│   │   │   ├── controller/
│   │   │   │   ├── McpHttpController.java       # MCP 协议控制器
│   │   │   │   └── RootController.java          # 根路径控制器
│   │   │   ├── mcp/
│   │   │   │   └── model/
│   │   │   │       ├── JsonRpcRequest.java      # JSON-RPC 请求模型
│   │   │   │       ├── JsonRpcResponse.java     # JSON-RPC 响应模型
│   │   │   │       └── JsonRpcError.java        # JSON-RPC 错误模型
│   │   │   ├── model/
│   │   │   │   └── HealthCheck.java             # 体检数据模型
│   │   │   └── service/
│   │   │       └── HealthCheckService.java      # 体检数据服务
│   │   └── resources/
│   │       ├── application.properties           # 应用配置
│   │       ├── schema.sql                       # 数据库表结构
│   │       └── data.sql                         # 初始化数据
│   └── test/
│       └── java/com/example/demo/
│           └── HealthCheckApplicationTests.java # 测试类
├── .mvn/wrapper/                                # Maven Wrapper
├── mvnw                                         # Maven Wrapper 脚本 (Unix)
├── mvnw.cmd                                     # Maven Wrapper 脚本 (Windows)
├── pom.xml                                      # Maven 项目配置
├── .gitignore                                   # Git 忽略文件
├── .gitattributes                               # Git 属性配置
└── README.md                                    # 项目说明文档
```

## 数据库表结构

```sql
CREATE TABLE health_check (
    id               SERIAL PRIMARY KEY,
    person_name      VARCHAR(100) NOT NULL,
    gender           VARCHAR(10),
    age              INTEGER,
    check_date       DATE DEFAULT CURRENT_DATE,
    wbc_count        DOUBLE PRECISION,
    neutrophil_pct   DOUBLE PRECISION,
    lymphocyte_pct   DOUBLE PRECISION,
    monocyte_pct     DOUBLE PRECISION,
    neutrophil_count DOUBLE PRECISION
);
```

## 依赖说明

### 主要依赖项

| 依赖 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 2.7.14 | 主框架 |
| PostgreSQL Driver | runtime | 数据库驱动 |
| Jackson | 2.13.x | JSON 处理 |
| Spring Boot JDBC | 2.7.14 | 数据库访问 |
| Spring Boot Web | 2.7.14 | Web 服务支持 |
| Spring Boot Test | 2.7.14 | 测试框架 |

### Java 版本要求
- Java 11 或更高版本

## 配置说明

### application.properties 配置项

```properties
# 应用基本配置
spring.application.name=health-check-mcp    # 应用名称
server.port=8081                            # 服务端口

# 数据库连接配置
spring.datasource.url=********************************************
spring.datasource.username=mcpuser
spring.datasource.password=mcppass
spring.datasource.driver-class-name=org.postgresql.Driver

# 数据库初始化配置
spring.sql.init.mode=always                 # 总是执行初始化脚本
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql

# JSON 序列化配置
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.serialization.fail-on-empty-beans=false

# HTTP 编码配置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 日志配置
logging.level.root=INFO
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG
```

## 快速开始

### 1. 启动 PostgreSQL 数据库

```bash
docker run --name postgres-mcp \
  -e POSTGRES_USER=mcpuser \
  -e POSTGRES_PASSWORD=mcppass \
  -e POSTGRES_DB=mcpdb \
  -p 5432:5432 \
  -d postgres:13
```

### 2. 验证数据库连接

```bash
# 进入容器
docker exec -it postgres-mcp psql -U mcpuser -d mcpdb

# 查看表结构
\dt
\d health_check

# 查看数据
SELECT * FROM health_check;
```

### 3. 启动应用

```bash
# 使用 Maven Wrapper
./mvnw spring-boot:run

# 或者先编译再运行
./mvnw clean package
java -jar target/health-check-mcp-0.0.1-SNAPSHOT.jar
```

### 4. 验证服务启动

```bash
curl http://localhost:8081/
```

预期响应：
```json
{
  "service": "health-check-mcp",
  "status": "running",
  "endpoints": {
    "mcp_rpc": "/mcp/rpc",
    "mcp_sse": "/mcp/sse",
    "health": "/mcp/health"
  }
}
```

## API 接口文档

### 1. 根路径信息
```
GET /
```
返回服务基本信息和可用端点。

### 2. MCP JSON-RPC 接口
```
POST /mcp/rpc
Content-Type: application/json
```

#### 初始化请求
```bash
curl -X POST http://localhost:8081/mcp/rpc \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {
        "name": "dify",
        "version": "1.0.0"
      }
    }
  }'
```

#### 获取工具列表
```bash
curl -X POST http://localhost:8081/mcp/rpc \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list",
    "params": {}
  }'
```

#### 调用体检查询工具
```bash
curl -X POST http://localhost:8081/mcp/rpc \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "get_health_check",
      "arguments": {
        "person_name": "张三"
      }
    }
  }'
```

### 3. SSE 连接接口
```
GET /mcp/sse
Accept: text/event-stream
```

```bash
curl -N -H "Accept: text/event-stream" http://localhost:8081/mcp/sse
```

### 4. 健康检查接口
```
GET /mcp/health
```

```bash
curl http://localhost:8081/mcp/health
```

预期响应：
```json
{
  "status": "ok",
  "service": "health-check-mcp",
  "timestamp": 1703123456789,
  "version": "1.0.0",
  "database_status": "connected"
}
```

## 部署指南

### 方式一：本地运行

1. 确保 Java 11+ 已安装
2. 启动 PostgreSQL 数据库
3. 修改 `application.properties` 中的数据库连接信息
4. 运行 `./mvnw spring-boot:run`

### 方式二：Docker 部署

#### 创建 Dockerfile
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app
COPY target/health-check-mcp-*.jar app.jar

EXPOSE 8081

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 使用 Docker Compose
```yaml
version: '3.8'
services:
  health-check-mcp:
    build: .
    ports:
      - "8081:8081"
    environment:
      - SPRING_DATASOURCE_URL=*************************************
    depends_on:
      - postgres

  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: mcpuser
      POSTGRES_PASSWORD: mcppass
      POSTGRES_DB: mcpdb
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

#### 部署命令
```bash
# 编译应用
./mvnw clean package

# 启动服务
docker-compose up -d
```

### 方式三：内网穿透部署

#### 使用 ngrok
```bash
# 启动应用
./mvnw spring-boot:run

# 在另一个终端启动 ngrok
ngrok http 8081
```

#### 使用 frp
创建 `frpc.ini`：
```ini
[common]
server_addr = your-frp-server.com
server_port = 7000

[health-check-mcp]
type = http
local_ip = 127.0.0.1
local_port = 8081
custom_domains = your-domain.com
```

## Dify 集成指南

### 1. 在 Dify 中添加 MCP 服务

1. 登录 Dify 管理后台
2. 进入 "工具" -> "自定义工具"
3. 选择 "MCP 服务"
4. 填写配置信息：

| 配置项 | 值 |
|--------|-----|
| 服务名称 | 体检指标查询服务 |
| 服务端点 | `https://9e15105bcd41.ngrok-free.app/mcp/rpc` |
| 协议版本 | `2024-11-05` |
| 连接方式 | HTTP |

### 2. 测试连接

在 Dify 中测试工具连接：
- 工具名称：`get_health_check`
- 参数：`{"person_name": "张三"}`

### 3. 在应用中使用

创建 Dify 应用时，在工具配置中启用 "体检指标查询服务"，用户就可以通过自然语言查询体检数据。

示例对话：
- 用户："帮我查询张三的体检指标"
- AI：调用 `get_health_check` 工具，返回张三的详细体检数据

## 示例数据

系统预置了以下测试数据：

| 姓名 | 性别 | 年龄 | 白细胞计数 | 中性粒细胞% |
|------|------|------|------------|-------------|
| 张三 | 男 | 35 | 6.30 | 62.4 |
| 李四 | 女 | 28 | 5.80 | 58.2 |
| 王五 | 男 | 42 | 7.10 | 65.8 |

## 故障排除

### 常见问题

#### 1. 数据库连接失败
**错误信息**：`Connection refused` 或 `Unknown database`

**解决方案**：
- 检查 PostgreSQL 是否正在运行
- 验证数据库连接信息是否正确
- 确保数据库已创建且用户有访问权限

```bash
# 检查数据库连接
docker exec -it postgres-mcp psql -U mcpuser -d mcpdb -c "SELECT 1;"
```

#### 2. 端口被占用
**错误信息**：`Port 8081 was already in use`

**解决方案**：
- 修改 `application.properties` 中的 `server.port`
- 或者停止占用端口的进程

```bash
# 查找占用端口的进程
lsof -i :8081
# 或者
netstat -tulpn | grep 8081
```

#### 3. CORS 跨域问题
**错误信息**：`Access to fetch at ... has been blocked by CORS policy`

**解决方案**：
- 检查 `DifyMcpConfig` 中的 CORS 配置
- 确保允许的域名包含 Dify 的域名

#### 4. JSON-RPC 调用失败
**错误信息**：`Method not found` 或 `Invalid params`

**解决方案**：
- 检查请求格式是否符合 JSON-RPC 2.0 规范
- 验证方法名和参数是否正确
- 查看应用日志获取详细错误信息

```bash
# 查看应用日志
tail -f logs/spring.log
```

### 调试技巧

#### 启用详细日志
在 `application.properties` 中添加：
```properties
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG
```

#### 使用健康检查端点
```bash
curl http://localhost:8081/mcp/health
```

#### 测试数据库连接
```bash
curl -X POST http://localhost:8081/mcp/rpc \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "get_health_check",
      "arguments": {
        "person_name": "张三"
      }
    }
  }'
```

## 开发指南

### 添加新的体检指标

1. 修改 `schema.sql` 添加新字段
2. 更新 `HealthCheck` 模型类
3. 修改 `HealthCheckService` 中的查询逻辑
4. 更新响应格式化逻辑

### 扩展 MCP 工具

1. 在 `McpHttpController` 中添加新的工具定义
2. 实现对应的业务逻辑
3. 更新工具列表返回

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 联系方式

如有问题，请通过 GitHub Issues 联系。 